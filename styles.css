/* Course Tree Styles */
.course-tree {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.tree-item {
    margin: 0;
    padding: 0;
    list-style: none;
}

.tree-node {
    position: relative;
    padding: 8px 12px;
    margin: 2px 0;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid transparent;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.tree-node:hover {
    background-color: #f8f9fa;
    border-color: #dee2e6;
}

.tree-node.selected {
    background-color: #e3f2fd;
    border-color: #2196f3;
    box-shadow: 0 2px 4px rgba(33, 150, 243, 0.1);
}

.tree-node-content {
    display: flex;
    align-items: center;
    flex: 1;
}

.tree-node-actions {
    display: flex;
    gap: 4px;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.tree-node:hover .tree-node-actions {
    opacity: 1;
}

.tree-toggle {
    width: 20px;
    height: 20px;
    border: none;
    background: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 8px;
    border-radius: 3px;
    transition: background-color 0.2s ease;
}

.tree-toggle:hover {
    background-color: #e9ecef;
}

.tree-toggle i {
    font-size: 12px;
    transition: transform 0.2s ease;
}

.tree-toggle.expanded i {
    transform: rotate(90deg);
}

.tree-icon {
    margin-right: 8px;
    width: 16px;
    text-align: center;
}

.tree-title {
    font-weight: 500;
    color: #333;
}

.tree-meta {
    font-size: 0.85em;
    color: #6c757d;
    margin-left: 8px;
}

.tree-children {
    margin-left: 24px;
    border-left: 2px solid #e9ecef;
    padding-left: 12px;
    margin-top: 4px;
}

.tree-children.collapsed {
    display: none;
}

/* Type-specific styling */
.tree-node[data-type="subject"] {
    background-color: #fff3e0;
    border-color: #ffcc02;
}

.tree-node[data-type="subject"].selected {
    background-color: #ffe0b2;
    border-color: #ff9800;
}

.tree-node[data-type="lesson"] {
    background-color: #f3e5f5;
    border-color: #ce93d8;
}

.tree-node[data-type="lesson"].selected {
    background-color: #e1bee7;
    border-color: #9c27b0;
}

.tree-node[data-type="inner_lesson"] {
    background-color: #e8f5e8;
    border-color: #a5d6a7;
}

.tree-node[data-type="inner_lesson"].selected {
    background-color: #c8e6c9;
    border-color: #4caf50;
}

/* Access type badges */
.access-badge {
    font-size: 0.75em;
    padding: 2px 6px;
    border-radius: 12px;
    font-weight: 500;
}

.access-free {
    background-color: #d4edda;
    color: #155724;
}

.access-paid {
    background-color: #fff3cd;
    color: #856404;
}

.access-restricted {
    background-color: #f8d7da;
    color: #721c24;
}

/* Material items */
.material-item {
    padding: 12px;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    margin-bottom: 8px;
    transition: all 0.2s ease;
}

.material-item:hover {
    border-color: #adb5bd;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.material-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;
}

.material-title {
    font-weight: 500;
    color: #333;
    margin: 0;
}

.material-type {
    font-size: 0.8em;
    padding: 2px 8px;
    border-radius: 12px;
    background-color: #e9ecef;
    color: #495057;
}

.material-description {
    font-size: 0.9em;
    color: #6c757d;
    margin: 0;
}

.material-actions {
    display: flex;
    gap: 4px;
    margin-top: 8px;
}

/* Status indicators */
.status-active {
    color: #28a745;
}

.status-inactive {
    color: #6c757d;
}

.status-draft {
    color: #ffc107;
}

.status-published {
    color: #28a745;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .tree-children {
        margin-left: 16px;
        padding-left: 8px;
    }
    
    .tree-node {
        padding: 6px 8px;
    }
    
    .tree-meta {
        display: none;
    }
}

/* Loading states */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Empty states */
.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 16px;
    opacity: 0.5;
}

/* Action buttons */
.btn-action {
    padding: 4px 8px;
    font-size: 0.8em;
    border-radius: 4px;
}

/* Drag and drop styles (for future enhancement) */
.drag-over {
    background-color: #e3f2fd !important;
    border-color: #2196f3 !important;
}

.dragging {
    opacity: 0.5;
}
