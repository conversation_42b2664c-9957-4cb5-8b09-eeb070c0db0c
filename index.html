<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Course Content Management</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">
    <link href="styles.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <!-- Header -->
        <div class="row">
            <div class="col-12">
                <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
                    <div class="container-fluid">
                        <a class="navbar-brand" href="#">
                            <i class="bi bi-book"></i> Course Management
                        </a>
                        <div class="navbar-nav ms-auto">
                            <button class="btn btn-outline-light btn-sm" id="addUnitBtn">
                                <i class="bi bi-plus-circle"></i> Add Unit
                            </button>
                            <button class="btn btn-outline-light btn-sm ms-2" id="addMaterialBtn">
                                <i class="bi bi-file-plus"></i> Add Material
                            </button>
                        </div>
                    </div>
                </nav>
            </div>
        </div>

        <!-- Main Content -->
        <div class="row mt-3">
            <!-- Course Tree Structure -->
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="bi bi-diagram-3"></i> Course Structure
                        </h5>
                        <div class="btn-group btn-group-sm" role="group">
                            <button type="button" class="btn btn-outline-secondary" id="expandAllBtn">
                                <i class="bi bi-arrows-expand"></i> Expand All
                            </button>
                            <button type="button" class="btn btn-outline-secondary" id="collapseAllBtn">
                                <i class="bi bi-arrows-collapse"></i> Collapse All
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="courseTree" class="course-tree">
                            <!-- Course tree will be dynamically generated here -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- Details Panel -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-info-circle"></i> Details
                        </h5>
                    </div>
                    <div class="card-body" id="detailsPanel">
                        <div class="text-muted text-center py-5">
                            <i class="bi bi-cursor-text fs-1"></i>
                            <p class="mt-2">Select an item to view details</p>
                        </div>
                    </div>
                </div>

                <!-- Materials Panel -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-collection"></i> Materials
                        </h5>
                    </div>
                    <div class="card-body" id="materialsPanel">
                        <div class="text-muted text-center py-3">
                            <i class="bi bi-files fs-1"></i>
                            <p class="mt-2">Select a unit to view materials</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Unit Modal -->
    <div class="modal fade" id="addUnitModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Add New Unit</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addUnitForm">
                        <div class="mb-3">
                            <label for="unitTitle" class="form-label">Title</label>
                            <input type="text" class="form-control" id="unitTitle" required>
                        </div>
                        <div class="mb-3">
                            <label for="unitType" class="form-label">Type</label>
                            <select class="form-select" id="unitType" required>
                                <option value="">Select type...</option>
                                <option value="subject">Subject</option>
                                <option value="lesson">Lesson</option>
                                <option value="inner_lesson">Inner Lesson</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="unitParent" class="form-label">Parent Unit</label>
                            <select class="form-select" id="unitParent">
                                <option value="">No parent (root level)</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="unitDescription" class="form-label">Description</label>
                            <textarea class="form-control" id="unitDescription" rows="3"></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="unitAccess" class="form-label">Access Type</label>
                            <select class="form-select" id="unitAccess" required>
                                <option value="free">Free</option>
                                <option value="paid">Paid</option>
                                <option value="restricted">Restricted</option>
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="saveUnitBtn">Save Unit</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Material Modal -->
    <div class="modal fade" id="addMaterialModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Add New Material</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addMaterialForm">
                        <div class="mb-3">
                            <label for="materialTitle" class="form-label">Title</label>
                            <input type="text" class="form-control" id="materialTitle" required>
                        </div>
                        <div class="mb-3">
                            <label for="materialType" class="form-label">Type</label>
                            <select class="form-select" id="materialType" required>
                                <option value="">Select type...</option>
                                <option value="video">Video</option>
                                <option value="document">Document</option>
                                <option value="audio">Audio</option>
                                <option value="text">Text</option>
                                <option value="quiz">Quiz</option>
                                <option value="exam">Exam</option>
                                <option value="assignment">Assignment</option>
                                <option value="other">Other</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="materialUnit" class="form-label">Unit</label>
                            <select class="form-select" id="materialUnit" required>
                                <option value="">Select unit...</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="materialDescription" class="form-label">Description</label>
                            <textarea class="form-control" id="materialDescription" rows="3"></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="materialAccess" class="form-label">Access Type</label>
                            <select class="form-select" id="materialAccess" required>
                                <option value="free">Free</option>
                                <option value="paid">Paid</option>
                                <option value="restricted">Restricted</option>
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="saveMaterialBtn">Save Material</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="script.js"></script>
</body>
</html>
