# Course Content Management System

A responsive web-based course content management system built with Bootstrap 5 that handles nested course structures at any level.

## Features

### 🌳 Hierarchical Course Structure
- **Unlimited nesting levels**: Support for any depth of course organization
- **Three unit types**: Subject → Lesson → Inner Lesson (with unlimited inner lesson nesting)
- **Visual tree structure**: Expandable/collapsible tree view with intuitive icons
- **Drag-and-drop ready**: CSS classes prepared for future drag-and-drop functionality

### 📚 Content Management
- **Course Units**: Create, edit, and delete subjects, lessons, and inner lessons
- **Study Materials**: Attach various types of materials to any unit level
- **Material Types**: Video, Document, Audio, Text, Quiz, Exam, Assignment, and Other
- **Access Control**: Free, Paid, and Restricted access levels

### 🎨 User Interface
- **Bootstrap 5**: Modern, responsive design
- **Interactive Tree**: Click to expand/collapse, select units for details
- **Real-time Updates**: Immediate UI updates when adding/removing content
- **Type-specific Styling**: Different colors and icons for each unit type
- **Material Counters**: Shows number of materials per unit

### 🔧 Management Features
- **Add Units**: Modal forms for creating new course units
- **Add Materials**: Attach study materials to specific units
- **Bulk Operations**: Expand/collapse all nodes at once
- **Details Panel**: View comprehensive unit information
- **Materials Panel**: Manage materials for selected units

## File Structure

```
course_management/
├── index.html          # Main HTML page
├── styles.css          # Custom CSS styles
├── script.js           # JavaScript functionality
├── course-data.json    # Demo data (JSON structure)
└── README.md          # This file
```

## Getting Started

1. **Open the application**: Simply open `index.html` in a web browser
2. **Explore the demo data**: The system loads with sample course content
3. **Navigate the tree**: Click on units to view details and materials
4. **Add content**: Use the "Add Unit" and "Add Material" buttons
5. **Manage structure**: Use expand/collapse controls and action buttons

## Data Structure

### Course Units Table Structure
```sql
CREATE TABLE course_units (
    id bigint unsigned NOT NULL AUTO_INCREMENT,
    course_id bigint unsigned NOT NULL,
    parent_id bigint unsigned DEFAULT NULL,
    title varchar(255) NOT NULL,
    slug varchar(255) NOT NULL,
    type enum('subject','lesson','inner_lesson') NOT NULL DEFAULT 'lesson',
    description text,
    access_type enum('free','paid','restricted') NOT NULL DEFAULT 'paid',
    sort_order int unsigned NOT NULL DEFAULT '0',
    status enum('active','inactive') NOT NULL DEFAULT 'active',
    -- Additional fields...
    PRIMARY KEY (id)
);
```

### Course Materials Table Structure
```sql
CREATE TABLE course_materials (
    id bigint unsigned NOT NULL AUTO_INCREMENT,
    course_id bigint unsigned NOT NULL,
    course_unit_id bigint unsigned DEFAULT NULL,
    type enum('video','document','audio','text','scorm','live_class','quiz','exam','assignment','other') NOT NULL,
    title varchar(255) NOT NULL,
    description text,
    access_type enum('free','paid','restricted') NOT NULL DEFAULT 'paid',
    sort_order int unsigned NOT NULL DEFAULT '0',
    status enum('draft','published') NOT NULL DEFAULT 'draft',
    -- Additional fields...
    PRIMARY KEY (id)
);
```

## Usage Examples

### Case 1: Course → Subject → Lesson → Inner Lesson → Study Materials
```
📚 Complete Web Development Course
├── 📂 Frontend Development (Subject)
│   ├── 📖 HTML & CSS Fundamentals (Lesson)
│   │   ├── 📄 HTML Basics (Inner Lesson)
│   │   │   ├── 🎥 HTML Introduction Video
│   │   │   └── 📋 HTML Cheat Sheet
│   │   └── 📄 CSS Styling (Inner Lesson)
│   │       └── 🎥 CSS Fundamentals
```

### Case 2: Course → Subject → Lesson → Study Materials
```
📚 Complete Web Development Course
├── 📂 Backend Development (Subject)
│   └── 📖 Node.js Basics (Lesson)
│       └── 📋 Node.js Installation Guide
```

### Case 3: Course → Subject → Lesson → Inner Lesson → Inner Lesson → Study Materials
```
📚 Complete Web Development Course
├── 📂 Frontend Development (Subject)
│   └── 📖 HTML & CSS Fundamentals (Lesson)
│       └── 📄 CSS Styling (Inner Lesson)
│           └── 📄 CSS Selectors (Inner Lesson)
│               └── ❓ CSS Selectors Quiz
```

## Key Features Explained

### 🎯 Unlimited Nesting
The system supports any level of nesting through the `parent_id` relationship. Each unit can have children, and those children can have their own children, creating unlimited depth.

### 🎨 Visual Hierarchy
- **Subjects**: Orange background with collection icon
- **Lessons**: Purple background with book icon  
- **Inner Lessons**: Green background with document icon
- **Materials**: Type-specific icons (video, document, quiz, etc.)

### 🔐 Access Control
- **Free**: Green badge, accessible to all users
- **Paid**: Yellow badge, requires payment
- **Restricted**: Red badge, special access required

### 📱 Responsive Design
The interface adapts to different screen sizes:
- Desktop: Full tree view with details panel
- Tablet: Optimized spacing and controls
- Mobile: Simplified view with essential information

## Backend Integration Notes

This is a frontend-only implementation. For full functionality, you'll need to:

1. **API Endpoints**: Create REST APIs for CRUD operations
2. **Database**: Implement the provided table structures
3. **Authentication**: Add user authentication and authorization
4. **File Upload**: Handle material file uploads
5. **Validation**: Server-side validation for data integrity

## Browser Compatibility

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## Dependencies

- **Bootstrap 5.3.2**: UI framework
- **Bootstrap Icons 1.11.1**: Icon library
- **Modern JavaScript**: ES6+ features used

## Future Enhancements

- Drag-and-drop reordering
- Bulk operations (move, copy, delete)
- Search and filtering
- Export/import functionality
- Real-time collaboration
- Advanced material preview
- Progress tracking
- Analytics dashboard

## License

This project is provided as-is for educational and development purposes.
