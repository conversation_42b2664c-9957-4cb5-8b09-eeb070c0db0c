{"course": {"id": 1, "title": "Complete Web Development Course", "description": "Learn full-stack web development from basics to advanced"}, "course_units": [{"id": 1, "course_id": 1, "parent_id": null, "title": "Frontend Development", "slug": "frontend-development", "type": "subject", "description": "Learn frontend technologies", "access_type": "paid", "sort_order": 1, "status": "active", "children": [{"id": 2, "course_id": 1, "parent_id": 1, "title": "HTML & CSS Fundamentals", "slug": "html-css-fundamentals", "type": "lesson", "description": "Basic HTML and CSS concepts", "access_type": "free", "sort_order": 1, "status": "active", "children": [{"id": 3, "course_id": 1, "parent_id": 2, "title": "HTML Basics", "slug": "html-basics", "type": "inner_lesson", "description": "Introduction to HTML", "access_type": "free", "sort_order": 1, "status": "active", "children": []}, {"id": 4, "course_id": 1, "parent_id": 2, "title": "CSS Styling", "slug": "css-styling", "type": "inner_lesson", "description": "CSS fundamentals and styling", "access_type": "free", "sort_order": 2, "status": "active", "children": [{"id": 5, "course_id": 1, "parent_id": 4, "title": "CSS Selectors", "slug": "css-selectors", "type": "inner_lesson", "description": "Understanding CSS selectors", "access_type": "free", "sort_order": 1, "status": "active", "children": []}]}]}, {"id": 6, "course_id": 1, "parent_id": 1, "title": "JavaScript Programming", "slug": "javascript-programming", "type": "lesson", "description": "Learn JavaScript from scratch", "access_type": "paid", "sort_order": 2, "status": "active", "children": [{"id": 7, "course_id": 1, "parent_id": 6, "title": "Variables and Data Types", "slug": "variables-data-types", "type": "inner_lesson", "description": "JavaScript variables and data types", "access_type": "paid", "sort_order": 1, "status": "active", "children": []}]}]}, {"id": 8, "course_id": 1, "parent_id": null, "title": "Backend Development", "slug": "backend-development", "type": "subject", "description": "Server-side development", "access_type": "paid", "sort_order": 2, "status": "active", "children": [{"id": 9, "course_id": 1, "parent_id": 8, "title": "Node.js Basics", "slug": "nodejs-basics", "type": "lesson", "description": "Introduction to Node.js", "access_type": "paid", "sort_order": 1, "status": "active", "children": []}]}], "course_materials": [{"id": 1, "course_id": 1, "course_unit_id": 3, "type": "video", "title": "HTML Introduction Video", "description": "Basic HTML concepts explained", "access_type": "free", "sort_order": 1, "status": "published"}, {"id": 2, "course_id": 1, "course_unit_id": 3, "type": "document", "title": "HTML Cheat Sheet", "description": "Quick reference for HTML tags", "access_type": "free", "sort_order": 2, "status": "published"}, {"id": 3, "course_id": 1, "course_unit_id": 4, "type": "video", "title": "CSS Fundamentals", "description": "Learn CSS basics", "access_type": "free", "sort_order": 1, "status": "published"}, {"id": 4, "course_id": 1, "course_unit_id": 5, "type": "quiz", "title": "CSS Selectors Quiz", "description": "Test your knowledge of CSS selectors", "access_type": "free", "sort_order": 1, "status": "published"}, {"id": 5, "course_id": 1, "course_unit_id": 7, "type": "video", "title": "JavaScript Variables Explained", "description": "Understanding JavaScript variables", "access_type": "paid", "sort_order": 1, "status": "published"}, {"id": 6, "course_id": 1, "course_unit_id": 9, "type": "document", "title": "Node.js Installation Guide", "description": "Step by step Node.js setup", "access_type": "paid", "sort_order": 1, "status": "published"}]}