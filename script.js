class CourseContentManager {
    constructor() {
        this.courseData = null;
        this.selectedUnit = null;
        this.expandedNodes = new Set();
        this.init();
    }

    async init() {
        try {
            await this.loadCourseData();
            this.renderCourseTree();
            this.bindEvents();
            this.populateDropdowns();
        } catch (error) {
            console.error('Failed to initialize course manager:', error);
            this.showError('Failed to load course data');
        }
    }

    async loadCourseData() {
        try {
            const response = await fetch('course-data.json');
            this.courseData = await response.json();
        } catch (error) {
            throw new Error('Failed to load course data');
        }
    }

    renderCourseTree() {
        const treeContainer = document.getElementById('courseTree');
        const rootUnits = this.courseData.course_units.filter(unit => !unit.parent_id);
        
        treeContainer.innerHTML = `
            <div class="course-header mb-3">
                <h6 class="text-primary">
                    <i class="bi bi-mortarboard"></i> ${this.courseData.course.title}
                </h6>
                <p class="text-muted small mb-0">${this.courseData.course.description}</p>
            </div>
            <ul class="tree-item">
                ${rootUnits.map(unit => this.renderTreeNode(unit)).join('')}
            </ul>
        `;
    }

    renderTreeNode(unit) {
        const children = this.courseData.course_units.filter(child => child.parent_id === unit.id);
        const hasChildren = children.length > 0;
        const isExpanded = this.expandedNodes.has(unit.id);
        const materials = this.courseData.course_materials.filter(material => material.course_unit_id === unit.id);
        
        const typeIcons = {
            'subject': 'bi-collection',
            'lesson': 'bi-book',
            'inner_lesson': 'bi-file-text'
        };

        return `
            <li class="tree-item">
                <div class="tree-node" data-unit-id="${unit.id}" data-type="${unit.type}">
                    <div class="tree-node-content">
                        ${hasChildren ? `
                            <button class="tree-toggle ${isExpanded ? 'expanded' : ''}" data-unit-id="${unit.id}">
                                <i class="bi bi-chevron-right"></i>
                            </button>
                        ` : '<span style="width: 20px; margin-right: 8px;"></span>'}
                        
                        <i class="tree-icon bi ${typeIcons[unit.type]}"></i>
                        <span class="tree-title">${unit.title}</span>
                        <span class="tree-meta">
                            <span class="access-badge access-${unit.access_type}">${unit.access_type}</span>
                            ${materials.length > 0 ? `<span class="badge bg-secondary ms-1">${materials.length} materials</span>` : ''}
                        </span>
                    </div>
                    <div class="tree-node-actions">
                        <button class="btn btn-sm btn-outline-primary btn-action" onclick="courseManager.editUnit(${unit.id})" title="Edit">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-success btn-action" onclick="courseManager.addChildUnit(${unit.id})" title="Add Child">
                            <i class="bi bi-plus"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-danger btn-action" onclick="courseManager.deleteUnit(${unit.id})" title="Delete">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </div>
                ${hasChildren ? `
                    <ul class="tree-children ${isExpanded ? '' : 'collapsed'}">
                        ${children.map(child => this.renderTreeNode(child)).join('')}
                    </ul>
                ` : ''}
            </li>
        `;
    }

    bindEvents() {
        // Tree toggle events
        document.addEventListener('click', (e) => {
            if (e.target.closest('.tree-toggle')) {
                const unitId = parseInt(e.target.closest('.tree-toggle').dataset.unitId);
                this.toggleNode(unitId);
            }
            
            if (e.target.closest('.tree-node') && !e.target.closest('.tree-node-actions')) {
                const unitId = parseInt(e.target.closest('.tree-node').dataset.unitId);
                this.selectUnit(unitId);
            }
        });

        // Expand/Collapse all buttons
        document.getElementById('expandAllBtn').addEventListener('click', () => this.expandAll());
        document.getElementById('collapseAllBtn').addEventListener('click', () => this.collapseAll());

        // Modal events
        document.getElementById('addUnitBtn').addEventListener('click', () => this.showAddUnitModal());
        document.getElementById('addMaterialBtn').addEventListener('click', () => this.showAddMaterialModal());
        document.getElementById('saveUnitBtn').addEventListener('click', () => this.saveUnit());
        document.getElementById('saveMaterialBtn').addEventListener('click', () => this.saveMaterial());
    }

    toggleNode(unitId) {
        if (this.expandedNodes.has(unitId)) {
            this.expandedNodes.delete(unitId);
        } else {
            this.expandedNodes.add(unitId);
        }
        this.renderCourseTree();
    }

    expandAll() {
        this.courseData.course_units.forEach(unit => {
            const hasChildren = this.courseData.course_units.some(child => child.parent_id === unit.id);
            if (hasChildren) {
                this.expandedNodes.add(unit.id);
            }
        });
        this.renderCourseTree();
    }

    collapseAll() {
        this.expandedNodes.clear();
        this.renderCourseTree();
    }

    selectUnit(unitId) {
        // Remove previous selection
        document.querySelectorAll('.tree-node.selected').forEach(node => {
            node.classList.remove('selected');
        });

        // Add selection to current node
        const selectedNode = document.querySelector(`[data-unit-id="${unitId}"]`);
        if (selectedNode) {
            selectedNode.classList.add('selected');
        }

        this.selectedUnit = this.courseData.course_units.find(unit => unit.id === unitId);
        this.showUnitDetails(this.selectedUnit);
        this.showUnitMaterials(unitId);
    }

    showUnitDetails(unit) {
        const detailsPanel = document.getElementById('detailsPanel');
        
        detailsPanel.innerHTML = `
            <div class="unit-details">
                <div class="d-flex justify-content-between align-items-start mb-3">
                    <h6 class="text-primary mb-0">${unit.title}</h6>
                    <span class="badge bg-${unit.type === 'subject' ? 'warning' : unit.type === 'lesson' ? 'info' : 'success'}">${unit.type}</span>
                </div>
                
                <div class="mb-3">
                    <small class="text-muted">Description:</small>
                    <p class="mb-0">${unit.description || 'No description available'}</p>
                </div>
                
                <div class="row g-2 mb-3">
                    <div class="col-6">
                        <small class="text-muted">Access Type:</small>
                        <div><span class="access-badge access-${unit.access_type}">${unit.access_type}</span></div>
                    </div>
                    <div class="col-6">
                        <small class="text-muted">Status:</small>
                        <div><i class="bi bi-circle-fill status-${unit.status}"></i> ${unit.status}</div>
                    </div>
                </div>
                
                <div class="mb-3">
                    <small class="text-muted">Sort Order:</small>
                    <div>${unit.sort_order}</div>
                </div>
                
                <div class="d-grid gap-2">
                    <button class="btn btn-outline-primary btn-sm" onclick="courseManager.editUnit(${unit.id})">
                        <i class="bi bi-pencil"></i> Edit Unit
                    </button>
                    <button class="btn btn-outline-success btn-sm" onclick="courseManager.addChildUnit(${unit.id})">
                        <i class="bi bi-plus"></i> Add Child Unit
                    </button>
                </div>
            </div>
        `;
    }

    showUnitMaterials(unitId) {
        const materialsPanel = document.getElementById('materialsPanel');
        const materials = this.courseData.course_materials.filter(material => material.course_unit_id === unitId);
        
        if (materials.length === 0) {
            materialsPanel.innerHTML = `
                <div class="empty-state">
                    <i class="bi bi-files"></i>
                    <p>No materials found</p>
                    <button class="btn btn-outline-primary btn-sm" onclick="courseManager.addMaterialToUnit(${unitId})">
                        <i class="bi bi-plus"></i> Add Material
                    </button>
                </div>
            `;
            return;
        }

        const typeIcons = {
            'video': 'bi-play-circle',
            'document': 'bi-file-text',
            'audio': 'bi-music-note',
            'text': 'bi-file-text',
            'quiz': 'bi-question-circle',
            'exam': 'bi-clipboard-check',
            'assignment': 'bi-pencil-square',
            'other': 'bi-file'
        };

        materialsPanel.innerHTML = `
            <div class="materials-list">
                ${materials.map(material => `
                    <div class="material-item">
                        <div class="material-header">
                            <h6 class="material-title">
                                <i class="bi ${typeIcons[material.type]}"></i>
                                ${material.title}
                            </h6>
                            <span class="material-type">${material.type}</span>
                        </div>
                        ${material.description ? `<p class="material-description">${material.description}</p>` : ''}
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="access-badge access-${material.access_type}">${material.access_type}</span>
                            <div class="material-actions">
                                <button class="btn btn-sm btn-outline-primary" onclick="courseManager.editMaterial(${material.id})" title="Edit">
                                    <i class="bi bi-pencil"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-danger" onclick="courseManager.deleteMaterial(${material.id})" title="Delete">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                `).join('')}
                <button class="btn btn-outline-primary btn-sm w-100 mt-2" onclick="courseManager.addMaterialToUnit(${unitId})">
                    <i class="bi bi-plus"></i> Add Material
                </button>
            </div>
        `;
    }

    populateDropdowns() {
        this.populateUnitParentDropdown();
        this.populateMaterialUnitDropdown();
    }

    populateUnitParentDropdown() {
        const dropdown = document.getElementById('unitParent');
        dropdown.innerHTML = '<option value="">No parent (root level)</option>';
        
        this.courseData.course_units.forEach(unit => {
            const option = document.createElement('option');
            option.value = unit.id;
            option.textContent = `${unit.title} (${unit.type})`;
            dropdown.appendChild(option);
        });
    }

    populateMaterialUnitDropdown() {
        const dropdown = document.getElementById('materialUnit');
        dropdown.innerHTML = '<option value="">Select unit...</option>';
        
        this.courseData.course_units.forEach(unit => {
            const option = document.createElement('option');
            option.value = unit.id;
            option.textContent = `${unit.title} (${unit.type})`;
            dropdown.appendChild(option);
        });
    }

    showAddUnitModal(parentId = null) {
        const modal = new bootstrap.Modal(document.getElementById('addUnitModal'));
        
        // Reset form
        document.getElementById('addUnitForm').reset();
        
        // Set parent if specified
        if (parentId) {
            document.getElementById('unitParent').value = parentId;
        }
        
        modal.show();
    }

    showAddMaterialModal(unitId = null) {
        const modal = new bootstrap.Modal(document.getElementById('addMaterialModal'));
        
        // Reset form
        document.getElementById('addMaterialForm').reset();
        
        // Set unit if specified
        if (unitId) {
            document.getElementById('materialUnit').value = unitId;
        }
        
        modal.show();
    }

    addChildUnit(parentId) {
        this.showAddUnitModal(parentId);
    }

    addMaterialToUnit(unitId) {
        this.showAddMaterialModal(unitId);
    }

    saveUnit() {
        const form = document.getElementById('addUnitForm');
        const formData = new FormData(form);
        
        // Basic validation
        if (!form.checkValidity()) {
            form.reportValidity();
            return;
        }

        // Create new unit object
        const newUnit = {
            id: Math.max(...this.courseData.course_units.map(u => u.id)) + 1,
            course_id: this.courseData.course.id,
            parent_id: document.getElementById('unitParent').value || null,
            title: document.getElementById('unitTitle').value,
            slug: this.generateSlug(document.getElementById('unitTitle').value),
            type: document.getElementById('unitType').value,
            description: document.getElementById('unitDescription').value,
            access_type: document.getElementById('unitAccess').value,
            sort_order: this.getNextSortOrder(document.getElementById('unitParent').value),
            status: 'active',
            children: []
        };

        // Add to course data
        this.courseData.course_units.push(newUnit);
        
        // Refresh UI
        this.renderCourseTree();
        this.populateDropdowns();
        
        // Close modal
        bootstrap.Modal.getInstance(document.getElementById('addUnitModal')).hide();
        
        this.showSuccess('Unit added successfully!');
    }

    saveMaterial() {
        const form = document.getElementById('addMaterialForm');
        
        // Basic validation
        if (!form.checkValidity()) {
            form.reportValidity();
            return;
        }

        // Create new material object
        const newMaterial = {
            id: Math.max(...this.courseData.course_materials.map(m => m.id)) + 1,
            course_id: this.courseData.course.id,
            course_unit_id: parseInt(document.getElementById('materialUnit').value),
            type: document.getElementById('materialType').value,
            title: document.getElementById('materialTitle').value,
            description: document.getElementById('materialDescription').value,
            access_type: document.getElementById('materialAccess').value,
            sort_order: this.getNextMaterialSortOrder(document.getElementById('materialUnit').value),
            status: 'published'
        };

        // Add to course data
        this.courseData.course_materials.push(newMaterial);
        
        // Refresh materials if the unit is currently selected
        if (this.selectedUnit && this.selectedUnit.id === newMaterial.course_unit_id) {
            this.showUnitMaterials(this.selectedUnit.id);
        }
        
        // Refresh tree to update material counts
        this.renderCourseTree();
        
        // Close modal
        bootstrap.Modal.getInstance(document.getElementById('addMaterialModal')).hide();
        
        this.showSuccess('Material added successfully!');
    }

    generateSlug(title) {
        return title.toLowerCase()
            .replace(/[^a-z0-9 -]/g, '')
            .replace(/\s+/g, '-')
            .replace(/-+/g, '-')
            .trim('-');
    }

    getNextSortOrder(parentId) {
        const siblings = this.courseData.course_units.filter(unit => 
            unit.parent_id === (parentId || null)
        );
        return siblings.length > 0 ? Math.max(...siblings.map(u => u.sort_order)) + 1 : 1;
    }

    getNextMaterialSortOrder(unitId) {
        const materials = this.courseData.course_materials.filter(material => 
            material.course_unit_id === parseInt(unitId)
        );
        return materials.length > 0 ? Math.max(...materials.map(m => m.sort_order)) + 1 : 1;
    }

    editUnit(unitId) {
        // Placeholder for edit functionality
        this.showInfo('Edit functionality will be implemented in the backend integration');
    }

    deleteUnit(unitId) {
        if (confirm('Are you sure you want to delete this unit? This action cannot be undone.')) {
            // Remove unit and its children
            this.removeUnitAndChildren(unitId);
            
            // Remove associated materials
            this.courseData.course_materials = this.courseData.course_materials.filter(
                material => material.course_unit_id !== unitId
            );
            
            // Refresh UI
            this.renderCourseTree();
            this.populateDropdowns();
            
            // Clear details if deleted unit was selected
            if (this.selectedUnit && this.selectedUnit.id === unitId) {
                document.getElementById('detailsPanel').innerHTML = `
                    <div class="text-muted text-center py-5">
                        <i class="bi bi-cursor-text fs-1"></i>
                        <p class="mt-2">Select an item to view details</p>
                    </div>
                `;
                document.getElementById('materialsPanel').innerHTML = `
                    <div class="text-muted text-center py-3">
                        <i class="bi bi-files fs-1"></i>
                        <p class="mt-2">Select a unit to view materials</p>
                    </div>
                `;
                this.selectedUnit = null;
            }
            
            this.showSuccess('Unit deleted successfully!');
        }
    }

    removeUnitAndChildren(unitId) {
        // Find children
        const children = this.courseData.course_units.filter(unit => unit.parent_id === unitId);
        
        // Recursively remove children
        children.forEach(child => this.removeUnitAndChildren(child.id));
        
        // Remove the unit itself
        this.courseData.course_units = this.courseData.course_units.filter(unit => unit.id !== unitId);
    }

    editMaterial(materialId) {
        // Placeholder for edit functionality
        this.showInfo('Edit functionality will be implemented in the backend integration');
    }

    deleteMaterial(materialId) {
        if (confirm('Are you sure you want to delete this material?')) {
            this.courseData.course_materials = this.courseData.course_materials.filter(
                material => material.id !== materialId
            );
            
            // Refresh materials if a unit is selected
            if (this.selectedUnit) {
                this.showUnitMaterials(this.selectedUnit.id);
            }
            
            // Refresh tree to update material counts
            this.renderCourseTree();
            
            this.showSuccess('Material deleted successfully!');
        }
    }

    showSuccess(message) {
        this.showToast(message, 'success');
    }

    showError(message) {
        this.showToast(message, 'danger');
    }

    showInfo(message) {
        this.showToast(message, 'info');
    }

    showToast(message, type = 'info') {
        // Create toast container if it doesn't exist
        let toastContainer = document.getElementById('toastContainer');
        if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.id = 'toastContainer';
            toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
            toastContainer.style.zIndex = '1055';
            document.body.appendChild(toastContainer);
        }

        // Create toast
        const toastId = 'toast-' + Date.now();
        const toastHtml = `
            <div id="${toastId}" class="toast" role="alert">
                <div class="toast-header">
                    <i class="bi bi-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-triangle' : 'info-circle'} text-${type} me-2"></i>
                    <strong class="me-auto">Course Manager</strong>
                    <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
                </div>
                <div class="toast-body">
                    ${message}
                </div>
            </div>
        `;

        toastContainer.insertAdjacentHTML('beforeend', toastHtml);
        
        const toastElement = document.getElementById(toastId);
        const toast = new bootstrap.Toast(toastElement);
        toast.show();

        // Remove toast element after it's hidden
        toastElement.addEventListener('hidden.bs.toast', () => {
            toastElement.remove();
        });
    }
}

// Initialize the course manager when the page loads
let courseManager;
document.addEventListener('DOMContentLoaded', () => {
    courseManager = new CourseContentManager();
});
